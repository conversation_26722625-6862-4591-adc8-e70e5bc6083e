
"**/__pycache__",
"**/*.so",
"**/.mypy_cache",
"**/.pytest_cache",
"**/.tox",
"**/venv",
"**/.venv*",
"**/env",
"**/env.bak",
"**/venv.bak",
"**/build",
"**/dist",
"**/*.egg-info",
"**/*.egg",
"**/node_modules",
"**/.git",
"**/.github",
"**/.vscode",
"**/.idea",
"**/*.log",
"**/logs",

"**/dataset*",
"**/migrations",
"**/local_settings.py",
"**/instance",
"**/.coverage",
"**/htmlcov",
"**/coverage.xml",
"**/.pytest_cache",
"**/.mypy_cache",
"**/*.sqlite3",
"**/*.db",
"**/src/data/transactions.db",
"**/test_CSVs",
"*.md",
"**/*.md",
"*.csv",
"*.CSV"
