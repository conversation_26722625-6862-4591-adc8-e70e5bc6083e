# Python bytecode
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
dist/
 build/
*.egg-info/
*setup.cfg


# Virtual environments
*.venv*
env/
ENV/
**/scripts/
scripts/
# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
Thumbs.db

# Application specific
logs/
*.log
# user_settings/
.pytest_cache/
.coverage
htmlcov/

# Local development
.env
.python-version
.ropeproject/

# Data files that shouldn't be in version control
*.db
*.sqlite
data/*.csv
**/z_archive*
*z_archive
**/z_old_structure_backup
**/z_proposed_view_interface
**/.AI_rules_other
**/.augmentignore
**/.kilocodemodes
**/.kilocode
**/.pytest_cache
**/.venv*
*.egg-info

*.log
