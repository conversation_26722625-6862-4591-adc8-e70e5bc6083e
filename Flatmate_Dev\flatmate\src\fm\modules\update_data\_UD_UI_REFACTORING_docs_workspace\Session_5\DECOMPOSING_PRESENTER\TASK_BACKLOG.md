# ✅ Task Backlog: UpdateDataPresenter Decomposition

This document contains a granular checklist of all tasks required for the refactoring. We will pull tasks from this backlog for each session.

---

### Phase 1: Project Setup & State Extraction

- [ ] **1.1** Create directory: `src/fm/modules/update_data/presenter/`
- [ ] **1.2** Create empty file: `src/fm/modules/update_data/presenter/__init__.py`
- [ ] **1.3** Create empty file: `src/fm/modules/update_data/presenter/state_manager.py`
- [ ] **1.4** Move `UpdateDataState` class definition from `ud_presenter.py` to `state_manager.py`.
- [ ] **1.5** In `ud_presenter.py`, remove the local `UpdateDataState` definition.
- [ ] **1.6** In `ud_presenter.py`, add `from .presenter.state_manager import UpdateDataState`.
- [ ] **1.7** Run all tests and confirm the application starts and runs without errors.

### Phase 2: Widget State Manager Extraction

- [ ] **2.1** Create empty file: `src/fm/modules/update_data/presenter/widget_state_manager.py`
- [ ] **2.2** In `widget_state_manager.py`, create the `WidgetStateManager` class.
- [ ] **2.3** Implement `__init__(self, view, state)` in `WidgetStateManager`.
- [ ] **2.4** Move method `_sync_state_to_view` from `ud_presenter.py` to `WidgetStateManager` and rename to `sync_state_to_view`.
- [ ] **2.5** Move method `_update_guide_pane` from `ud_presenter.py` to `WidgetStateManager` and rename to `update_guide_pane`.
- [ ] **2.6** Move methods `_update_guide_pane_for_folder` and `_update_guide_pane_for_files` to `WidgetStateManager`.
- [ ] **2.7** In `ud_presenter.py`, instantiate `self.widget_state_manager = WidgetStateManager(self.view, self.state)`.
- [ ] **2.8** In `ud_presenter.py`, replace all calls to the original methods with calls to `self.widget_state_manager`.
- [ ] **2.9** Run all tests and confirm UI updates correctly.

### Phase 3: Source Manager Extraction

- [ ] **3.1** Create empty file: `src/fm/modules/update_data/presenter/source_manager.py`
- [ ] **3.2** In `source_manager.py`, create the `SourceManager` class.
- [ ] **3.3** Implement `__init__(self, view, state, widget_manager)` in `SourceManager`.
- [ ] **3.4** Move all source-related methods (`_handle_source_select`, etc.) to `SourceManager`.
- [ ] **3.5** In `ud_presenter.py`, instantiate `self.source_manager`.
- [ ] **3.6** In `_connect_signals`, wire the `source_select_requested` signal to `self.source_manager.handle_source_select`.
- [ ] **3.7** Run all tests and confirm source selection works correctly.

### Phase 4: Archive & Processing Manager Extraction

- [ ] **4.1** Create empty files for `archive_manager.py` and `processing_manager.py`.
- [ ] **4.2** Implement `ArchiveManager` class and move `_handle_save_select` logic.
- [ ] **4.3** Implement `ProcessingManager` class and move `_handle_process_files` and `_on_processing_*` event handlers.
- [ ] **4.4** Instantiate managers in `ud_presenter.py`.
- [ ] **4.5** Connect signals for save and process to the new managers.
- [ ] **4.6** Run all tests and confirm the end-to-end workflow is successful.

### Phase 5: Final Review and Cleanup

- [ ] **5.1** Move `ud_presenter.py` to `presenter/update_data_presenter.py`.
- [ ] **5.2** Update all necessary imports that pointed to the old location.
- [ ] **5.3** Review all new manager classes for clarity and consistency.
- [ ] **5.4** Add docstrings and comments where needed.
- [ ] **5.5** Delete any old, unused methods from the main presenter.
- [ ] **5.6** Final test run.
